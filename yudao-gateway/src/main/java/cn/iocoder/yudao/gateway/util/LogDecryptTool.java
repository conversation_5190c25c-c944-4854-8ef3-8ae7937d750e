package cn.iocoder.yudao.gateway.util;

import cn.hutool.core.util.StrUtil;

/**
 * 日志解密工具类
 * 用于解密通过EncryptedAppender加密的日志内容
 *
 * <AUTHOR>
 */
public class LogDecryptTool {

    /**
     * 解密日志内容
     *
     * @param encryptedLog 加密的日志内容
     * @return 解密后的日志内容
     */
    public static String decryptLog(String encryptedLog) {
        if (StrUtil.isBlank(encryptedLog)) {
            return encryptedLog;
        }

        // 移除加密前缀
        String prefix = "[ENCRYPTED] ";
        if (encryptedLog.startsWith(prefix)) {
            String encryptedContent = encryptedLog.substring(prefix.length()).trim();
            return LogEncryptUtils.decrypt(encryptedContent);
        }

        return encryptedLog;
    }

    /**
     * 批量解密日志内容
     *
     * @param encryptedLogs 加密的日志内容数组
     * @return 解密后的日志内容数组
     */
    public static String[] decryptLogs(String[] encryptedLogs) {
        if (encryptedLogs == null || encryptedLogs.length == 0) {
            return encryptedLogs;
        }

        String[] decryptedLogs = new String[encryptedLogs.length];
        for (int i = 0; i < encryptedLogs.length; i++) {
            decryptedLogs[i] = decryptLog(encryptedLogs[i]);
        }

        return decryptedLogs;
    }

    /**
     * 主方法，用于命令行解密日志
     *
     * @param args 命令行参数，第一个参数为加密的日志内容
     */
    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("使用方法: java LogDecryptTool <加密的日志内容>");
            System.out.println("示例: java LogDecryptTool \"[ENCRYPTED] base64加密内容\"");
            return;
        }

        String encryptedLog = args[0];
        try {
            String decryptedLog = decryptLog(encryptedLog);
            System.out.println("解密后的日志内容:");
            System.out.println(decryptedLog);
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
        }
    }
}
