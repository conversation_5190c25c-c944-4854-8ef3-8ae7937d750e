package cn.iocoder.yudao.gateway.logging;

import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.ConsoleAppender;
import ch.qos.logback.core.encoder.Encoder;
import cn.iocoder.yudao.gateway.util.LogEncryptUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 加密控制台输出Appender
 * 继承自ConsoleAppender，对日志内容进行AES加密后输出
 *
 * <AUTHOR>
 */
public class EncryptedConsoleAppender extends ConsoleAppender<ILoggingEvent> {

    /**
     * 是否启用加密，默认为true
     */
    private boolean encryptEnabled = true;

    /**
     * 需要加密的日志级别，默认为INFO及以上
     */
    private String encryptLevel = "INFO";

    /**
     * 加密前缀，用于标识加密的日志
     */
    private String encryptPrefix = "[ENCRYPTED] ";

    @Override
    protected void writeOut(ILoggingEvent event) throws IOException {
        if (!encryptEnabled) {
            super.writeOut(event);
            return;
        }

        // 判断是否需要加密
        if (shouldEncrypt(event)) {
            // 获取原始日志内容
            byte[] originalBytes = this.encoder.encode(event);
            String originalLog = new String(originalBytes, StandardCharsets.UTF_8);
            
            // 加密日志内容
            String encryptedLog = LogEncryptUtils.encrypt(originalLog.trim());
            
            // 输出加密后的日志
            String finalLog = encryptPrefix + encryptedLog + System.lineSeparator();
            this.getOutputStream().write(finalLog.getBytes(StandardCharsets.UTF_8));
            this.getOutputStream().flush();
        } else {
            // 不需要加密，直接输出
            super.writeOut(event);
        }
    }

    /**
     * 判断是否需要加密
     *
     * @param event 日志事件
     * @return 是否需要加密
     */
    private boolean shouldEncrypt(ILoggingEvent event) {
        // 检查日志级别
        switch (encryptLevel.toUpperCase()) {
            case "TRACE":
                return true;
            case "DEBUG":
                return event.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.DEBUG);
            case "INFO":
                return event.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.INFO);
            case "WARN":
                return event.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.WARN);
            case "ERROR":
                return event.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.ERROR);
            default:
                return event.getLevel().isGreaterOrEqual(ch.qos.logback.classic.Level.INFO);
        }
    }

    // Getter and Setter methods
    public boolean isEncryptEnabled() {
        return encryptEnabled;
    }

    public void setEncryptEnabled(boolean encryptEnabled) {
        this.encryptEnabled = encryptEnabled;
    }

    public String getEncryptLevel() {
        return encryptLevel;
    }

    public void setEncryptLevel(String encryptLevel) {
        this.encryptLevel = encryptLevel;
    }

    public String getEncryptPrefix() {
        return encryptPrefix;
    }

    public void setEncryptPrefix(String encryptPrefix) {
        this.encryptPrefix = encryptPrefix;
    }
}
