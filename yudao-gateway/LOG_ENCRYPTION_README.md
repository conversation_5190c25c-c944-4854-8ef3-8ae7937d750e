# 日志加密功能说明

## 概述

本项目在Slf4j框架层面实现了日志加密功能，通过自定义的Logback Appender自动对输出的日志进行AES加密，无需在代码中手动调用加密方法。

## 功能特性

1. **自动加密**: 通过自定义Logback Appender自动对日志进行AES加密
2. **级别控制**: 可配置需要加密的日志级别（TRACE, DEBUG, INFO, WARN, ERROR）
3. **双重输出**: 同时支持加密和非加密日志输出
4. **解密工具**: 提供日志解密工具类，方便查看加密后的日志内容
5. **性能优化**: 支持异步日志输出，提升性能

## 配置说明

### 1. application.yaml配置

```yaml
# 日志加密配置
log:
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥
    enabled: true # 是否启用日志加密
    level: INFO # 需要加密的日志级别
```

### 2. logback-spring.xml配置

系统已配置以下Appender：

- `ENCRYPTED_STDOUT`: 加密控制台输出
- `ENCRYPTED_FILE`: 加密文件输出
- `ASYNC_ENCRYPTED`: 异步加密文件输出

## 使用方法

### 1. 正常使用Slf4j

```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class YourService {
    private static final Logger log = LoggerFactory.getLogger(YourService.class);
    
    public void someMethod() {
        // 这些日志会自动被加密（根据配置的级别）
        log.info("用户登录成功，用户ID：{}", userId);
        log.warn("API调用失败，错误码：{}", errorCode);
        log.error("数据库连接异常：{}", exception.getMessage());
    }
}
```

### 2. 查看加密后的日志

加密后的日志格式如下：
```
[ENCRYPTED] base64加密内容
```

### 3. 解密日志内容

#### 方法一：使用LogDecryptTool类

```java
String encryptedLog = "[ENCRYPTED] base64加密内容";
String decryptedLog = LogDecryptTool.decryptLog(encryptedLog);
System.out.println(decryptedLog);
```

#### 方法二：命令行解密

```bash
java -cp your-classpath cn.iocoder.yudao.gateway.util.LogDecryptTool "[ENCRYPTED] base64加密内容"
```

#### 方法三：直接使用加密工具类

```java
String encryptedContent = "base64加密内容";
String decryptedLog = LogEncryptUtils.decrypt(encryptedContent);
```

## 文件结构

```
yudao-gateway/
├── src/main/java/cn/iocoder/yudao/gateway/
│   ├── logging/
│   │   ├── EncryptedConsoleAppender.java  # 加密控制台输出Appender
│   │   └── EncryptedFileAppender.java     # 加密文件输出Appender
│   └── util/
│       ├── LogEncryptUtils.java           # 日志加密工具类
│       └── LogDecryptTool.java            # 日志解密工具类
├── src/main/resources/
│   ├── application.yaml                   # 应用配置（包含加密配置）
│   └── logback-spring.xml                 # Logback配置
└── src/test/java/cn/iocoder/yudao/gateway/
    └── logging/
        └── LogEncryptionTest.java         # 加密功能测试类
```

## 安全注意事项

1. **密钥管理**: 请妥善保管加密密钥，建议使用环境变量或配置中心管理
2. **密钥轮换**: 定期更换加密密钥，提高安全性
3. **访问控制**: 限制对加密日志文件的访问权限
4. **备份安全**: 确保日志备份的安全性

## 性能考虑

1. **异步输出**: 使用异步Appender提升性能
2. **级别控制**: 合理配置加密级别，避免对所有日志加密
3. **缓存机制**: AES实例采用单例模式，避免重复创建

## 故障排除

### 1. 加密失败
- 检查密钥配置是否正确
- 确认LogEncryptUtils类是否正确初始化

### 2. 解密失败
- 确认使用的密钥与加密时一致
- 检查加密内容格式是否正确

### 3. 日志不加密
- 检查logback-spring.xml配置是否正确
- 确认日志级别是否达到加密要求

## 测试

运行测试类验证功能：

```bash
mvn test -Dtest=LogEncryptionTest
```

## 扩展功能

可以根据需要扩展以下功能：

1. **选择性加密**: 根据日志内容或来源选择性加密
2. **多种加密算法**: 支持多种加密算法
3. **密钥管理**: 集成密钥管理系统
4. **审计日志**: 记录加密操作的审计日志
